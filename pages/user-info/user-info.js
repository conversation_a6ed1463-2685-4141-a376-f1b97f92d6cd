// pages/user-info/user-info.js
const config = require('../../config.js');

Page({
  data: {
    isLoading: false,
    registerInfo: null, // 从登录页面传来的注册信息
    
    // 用户信息表单
    userInfo: {
      name: '',
      gender: '',
      birthDate: '',
      birthTime: '',
      birthPlace: '',
      occupation: '',
      hobbies: ''
    }
  },

  onLoad: function (options) {
    console.log('[UserInfo] 页面加载');
    
    // 获取注册信息
    const registerInfo = wx.getStorageSync('registerInfo');
    if (!registerInfo) {
      wx.showToast({
        title: '请先完成登录信息填写',
        icon: 'none'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }
    
    this.setData({
      registerInfo: registerInfo
    });
    
    // 如果是微信注册，预填充一些信息
    if (registerInfo.registerType === 'wechat' && registerInfo.wechatUserInfo) {
      const wechatUserInfo = registerInfo.wechatUserInfo;
      this.setData({
        'userInfo.name': wechatUserInfo.nickName || '',
        'userInfo.gender': wechatUserInfo.gender === 1 ? '男' : wechatUserInfo.gender === 2 ? '女' : ''
      });
    }
  },

  // 获取API基础URL
  getApiBaseUrl: function() {
    const currentEnv = config.CURRENT_ENV;
    return config.API_ENV[currentEnv];
  },

  // 表单输入处理
  onNameInput: function(e) {
    this.setData({
      'userInfo.name': e.detail.value
    });
  },

  onGenderChange: function(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({
      'userInfo.gender': gender
    });
  },

  onBirthDateChange: function(e) {
    this.setData({
      'userInfo.birthDate': e.detail.value
    });
  },

  onBirthTimeChange: function(e) {
    this.setData({
      'userInfo.birthTime': e.detail.value
    });
  },

  onBirthPlaceInput: function(e) {
    this.setData({
      'userInfo.birthPlace': e.detail.value
    });
  },

  onOccupationInput: function(e) {
    this.setData({
      'userInfo.occupation': e.detail.value
    });
  },

  onHobbiesInput: function(e) {
    this.setData({
      'userInfo.hobbies': e.detail.value
    });
  },

  // 验证表单
  validateForm: function() {
    const { userInfo } = this.data;
    
    if (!userInfo.name.trim()) {
      wx.showToast({ title: '请输入姓名', icon: 'none' });
      return false;
    }
    
    if (!userInfo.birthDate) {
      wx.showToast({ title: '请选择出生日期', icon: 'none' });
      return false;
    }
    
    if (!userInfo.birthPlace.trim()) {
      wx.showToast({ title: '请输入出生地点', icon: 'none' });
      return false;
    }
    
    return true;
  },

  // 提交注册
  handleSubmit: function() {
    if (!this.validateForm()) {
      return;
    }
    
    const { registerInfo, userInfo } = this.data;
    
    this.setData({ isLoading: true });
    
    // 准备注册数据
    const registrationData = {
      // 基本注册信息
      registerType: registerInfo.registerType,
      email: registerInfo.email,
      phone: registerInfo.phone,
      password: registerInfo.password,
      wechatOpenId: registerInfo.wechatCode, // 暂时使用code作为openId
      wechatUnionId: registerInfo.wechatUserInfo?.unionId,
      
      // 个人信息
      name: userInfo.name.trim(),
      gender: userInfo.gender,
      birthDate: userInfo.birthDate,
      birthTime: userInfo.birthTime,
      birthPlace: userInfo.birthPlace.trim(),
      
      // 其他信息
      additionalInfo: {
        occupation: userInfo.occupation.trim(),
        hobbies: userInfo.hobbies.trim()
      }
    };
    
    console.log('[UserInfo] 提交注册数据:', registrationData);
    
    // 调用注册API
    wx.request({
      url: `${this.getApiBaseUrl()}/api/v1/user/register`,
      method: 'POST',
      data: registrationData,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('[UserInfo] 注册响应:', res.data);
        
        if (res.data.code === 200) {
          // 注册成功
          wx.showToast({
            title: '注册成功',
            icon: 'success'
          });
          
          // 保存用户信息
          wx.setStorageSync('userInfo', res.data.data);
          
          // 清除注册临时信息
          wx.removeStorageSync('registerInfo');
          
          // 跳转到主页
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/input-info/input-info'
            });
          }, 1500);
          
        } else {
          wx.showToast({
            title: res.data.message || '注册失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('[UserInfo] 注册请求失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 页面卸载时清理
  onUnload: function() {
    // 如果用户没有完成注册就离开页面，清除临时数据
    if (!wx.getStorageSync('userInfo')) {
      wx.removeStorageSync('registerInfo');
    }
  }
});
