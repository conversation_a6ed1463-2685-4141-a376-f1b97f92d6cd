<!--pages/user-info/user-info.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">完善个人信息</text>
    <text class="subtitle">请填写以下信息以完成注册</text>
  </view>

  <!-- 表单容器 -->
  <view class="form-container">
    <!-- 基本信息 -->
    <view class="section">
      <view class="section-title">基本信息</view>
      
      <view class="input-group">
        <text class="label">姓名 *</text>
        <input class="input" type="text" placeholder="请输入您的姓名" value="{{userInfo.name}}" bindinput="onNameInput" />
      </view>

      <view class="input-group">
        <text class="label">性别</text>
        <view class="radio-group">
          <label class="radio-item">
            <radio value="男" checked="{{userInfo.gender === '男'}}" bindtap="onGenderChange" data-gender="男" />
            <text>男</text>
          </label>
          <label class="radio-item">
            <radio value="女" checked="{{userInfo.gender === '女'}}" bindtap="onGenderChange" data-gender="女" />
            <text>女</text>
          </label>
        </view>
      </view>
    </view>

    <!-- 出生信息 -->
    <view class="section">
      <view class="section-title">出生信息</view>
      
      <view class="input-group">
        <text class="label">出生日期 *</text>
        <picker mode="date" value="{{userInfo.birthDate}}" bindchange="onBirthDateChange">
          <view class="picker-display">
            {{userInfo.birthDate || '请选择出生日期'}}
          </view>
        </picker>
      </view>

      <view class="input-group">
        <text class="label">出生时间</text>
        <picker mode="time" value="{{userInfo.birthTime}}" bindchange="onBirthTimeChange">
          <view class="picker-display">
            {{userInfo.birthTime || '请选择出生时间（可选）'}}
          </view>
        </picker>
      </view>

      <view class="input-group">
        <text class="label">出生地点 *</text>
        <input class="input" type="text" placeholder="请输入出生地点，如：四川省成都市" value="{{userInfo.birthPlace}}" bindinput="onBirthPlaceInput" />
        <text class="input-tip">用于计算真太阳时和地理位置信息</text>
      </view>
    </view>

    <!-- 其他信息 -->
    <view class="section">
      <view class="section-title">其他信息（可选）</view>
      
      <view class="input-group">
        <text class="label">职业</text>
        <input class="input" type="text" placeholder="请输入您的职业" value="{{userInfo.occupation}}" bindinput="onOccupationInput" />
      </view>

      <view class="input-group">
        <text class="label">兴趣爱好</text>
        <textarea class="textarea" placeholder="请简单描述您的兴趣爱好" value="{{userInfo.hobbies}}" bindinput="onHobbiesInput" maxlength="200"></textarea>
      </view>
    </view>

    <!-- 提交按钮 -->
    <button class="btn-submit" bindtap="handleSubmit" disabled="{{isLoading}}">
      {{isLoading ? '注册中...' : '完成注册'}}
    </button>
  </view>

  <!-- 提示信息 -->
  <view class="tips">
    <text class="tip-text">* 为必填项</text>
    <text class="tip-text">您的信息将用于生成个性化的分析报告</text>
  </view>
</view>
