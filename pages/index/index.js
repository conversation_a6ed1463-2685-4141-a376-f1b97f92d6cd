// index.js
Page({
  data: {
    bubbles: [
      { text: '个性化分析', delay: 0 },
      { text: '潜能发掘', delay: 2 },
      { text: '人生规划', delay: 4 }
    ]
  },

  onLoad() {
    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.userId) {
      // 用户已登录，可以执行原有逻辑或跳转到功能页
      // 每8秒重新触发一次气泡动画
      setInterval(() => {
        this.setData({
          bubbles: [
            { text: '个性化分析', delay: 0 },
            { text: '潜能发掘', delay: 2 },
            { text: '人生规划', delay: 4 }
          ]
        });
      }, 8000);
    } else {
      // 用户未登录，跳转到登录页
      wx.redirectTo({
        url: '/pages/login/login'
      });
    }
  },

  startTest() {
    wx.navigateTo({
      url: '/pages/input-info/input-info'
    })
  }
})