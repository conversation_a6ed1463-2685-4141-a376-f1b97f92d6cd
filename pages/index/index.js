// index.js
Page({
  data: {
    bubbles: [
      { text: '个性化分析', delay: 0 },
      { text: '潜能发掘', delay: 2 },
      { text: '人生规划', delay: 4 }
    ]
  },

  onLoad() {
    // 每8秒重新触发一次气泡动画
    setInterval(() => {
      this.setData({
        bubbles: [
          { text: '个性化分析', delay: 0 },
          { text: '潜能发掘', delay: 2 },
          { text: '人生规划', delay: 4 }
        ]
      });
    }, 8000);
  },

  startTest() {
    wx.navigateTo({
      url: '/pages/input-info/input-info'
    })
  }
})