// pages/login/login.js
const config = require('../../config.js');

Page({
  data: {
    currentTab: 'login', // 'login' 或 'register'
    loginType: 'email',  // 'email' 或 'phone'
    registerType: 'email', // 'email' 或 'phone'
    isLoading: false,
    
    // 登录表单数据
    loginForm: {
      email: '',
      phone: '',
      password: ''
    },
    
    // 注册表单数据
    registerForm: {
      email: '',
      phone: '',
      password: ''
    }
  },

  onLoad: function (options) {
    console.log('[Login] 页面加载');
    
    // 检查是否已经登录
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.userId) {
      console.log('[Login] 用户已登录，跳转到主页');
      wx.redirectTo({
        url: '/pages/input-info/input-info'
      });
    }
  },

  // 切换登录/注册标签
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });
  },

  // 切换登录类型
  switchLoginType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      loginType: type
    });
  },

  // 切换注册类型
  switchRegisterType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      registerType: type
    });
  },

  // 登录表单输入处理
  onEmailInput: function(e) {
    this.setData({
      'loginForm.email': e.detail.value
    });
  },

  onPhoneInput: function(e) {
    this.setData({
      'loginForm.phone': e.detail.value
    });
  },

  onPasswordInput: function(e) {
    this.setData({
      'loginForm.password': e.detail.value
    });
  },

  // 注册表单输入处理
  onRegisterEmailInput: function(e) {
    this.setData({
      'registerForm.email': e.detail.value
    });
  },

  onRegisterPhoneInput: function(e) {
    this.setData({
      'registerForm.phone': e.detail.value
    });
  },

  onRegisterPasswordInput: function(e) {
    this.setData({
      'registerForm.password': e.detail.value
    });
  },

  // 获取API基础URL
  getApiBaseUrl: function() {
    const currentEnv = config.CURRENT_ENV;
    return config.API_ENV[currentEnv];
  },

  // 处理登录
  handleLogin: function() {
    const { loginType, loginForm } = this.data;
    
    // 验证输入
    let identifier = '';
    if (loginType === 'email') {
      if (!loginForm.email) {
        wx.showToast({ title: '请输入邮箱地址', icon: 'none' });
        return;
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(loginForm.email)) {
        wx.showToast({ title: '邮箱格式不正确', icon: 'none' });
        return;
      }
      identifier = loginForm.email;
    } else {
      if (!loginForm.phone) {
        wx.showToast({ title: '请输入手机号', icon: 'none' });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(loginForm.phone)) {
        wx.showToast({ title: '手机号格式不正确', icon: 'none' });
        return;
      }
      identifier = loginForm.phone;
    }

    if (!loginForm.password) {
      wx.showToast({ title: '请输入密码', icon: 'none' });
      return;
    }

    this.setData({ isLoading: true });

    // 调用登录API
    wx.request({
      url: `${this.getApiBaseUrl()}/api/v1/user/login`,
      method: 'POST',
      data: {
        loginType: loginType,
        identifier: identifier,
        password: loginForm.password
      },
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('[Login] 登录响应:', res.data);
        
        if (res.data.code === 200) {
          // 登录成功，保存用户信息
          wx.setStorageSync('userInfo', res.data.data);
          
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });

          // 跳转到主页
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/input-info/input-info'
            });
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.message || '登录失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('[Login] 登录请求失败:', error);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 处理注册
  handleRegister: function() {
    const { registerType, registerForm } = this.data;
    
    // 验证输入
    if (registerType === 'email') {
      if (!registerForm.email) {
        wx.showToast({ title: '请输入邮箱地址', icon: 'none' });
        return;
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerForm.email)) {
        wx.showToast({ title: '邮箱格式不正确', icon: 'none' });
        return;
      }
    } else {
      if (!registerForm.phone) {
        wx.showToast({ title: '请输入手机号', icon: 'none' });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(registerForm.phone)) {
        wx.showToast({ title: '手机号格式不正确', icon: 'none' });
        return;
      }
    }

    if (!registerForm.password) {
      wx.showToast({ title: '请输入密码', icon: 'none' });
      return;
    }

    if (registerForm.password.length < 6) {
      wx.showToast({ title: '密码至少6位', icon: 'none' });
      return;
    }

    // 保存注册信息到本地，跳转到信息填写页面
    const registerInfo = {
      registerType: registerType,
      email: registerType === 'email' ? registerForm.email : null,
      phone: registerType === 'phone' ? registerForm.phone : null,
      password: registerForm.password
    };

    wx.setStorageSync('registerInfo', registerInfo);

    // 跳转到信息填写页面
    wx.navigateTo({
      url: '/pages/user-info/user-info'
    });
  },

  // 处理微信登录
  handleWechatLogin: function(e) {
    console.log('[Login] 微信登录:', e.detail);
    
    if (e.detail.userInfo) {
      // 获取微信授权信息
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('[Login] 微信登录code:', res.code);
            
            // 这里应该调用后端API验证微信登录
            // 暂时模拟登录成功
            wx.showToast({
              title: '微信登录功能开发中',
              icon: 'none'
            });
          }
        }
      });
    } else {
      wx.showToast({
        title: '需要授权才能登录',
        icon: 'none'
      });
    }
  },

  // 处理微信注册
  handleWechatRegister: function(e) {
    console.log('[Login] 微信注册:', e.detail);
    
    if (e.detail.userInfo) {
      // 获取微信授权信息
      wx.login({
        success: (res) => {
          if (res.code) {
            console.log('[Login] 微信注册code:', res.code);
            
            // 保存微信信息，跳转到信息填写页面
            const wechatInfo = {
              registerType: 'wechat',
              wechatCode: res.code,
              wechatUserInfo: e.detail.userInfo
            };

            wx.setStorageSync('registerInfo', wechatInfo);

            // 跳转到信息填写页面
            wx.navigateTo({
              url: '/pages/user-info/user-info'
            });
          }
        }
      });
    } else {
      wx.showToast({
        title: '需要授权才能注册',
        icon: 'none'
      });
    }
  }
});
