const express = require('express');
const router = express.Router();
const { validateAnalysis } = require('../middlewares/validator');
const auth = require('../middlewares/auth');
const deepseekService = require('../services/deepseek');
// 直接引入Python八字计算模块，跳过baziUtils中间层
const pythonBazi = require('../utils/python-bazi');
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');
// 引入请求锁定服务
const requestLock = require('../services/request-lock');

/**
 * @api {post} /api/v1/calculate-bazi 计算八字
 * @apiName CalculateBazi
 * @apiGroup Bazi
 * @apiDescription 计算用户的八字信息，包括四柱、大运和流年（注意：传入的时间必须是已经计算好的真太阳时）
 *
 * @apiParam {Number} year 出生年（真太阳时）
 * @apiParam {Number} month 出生月（真太阳时）
 * @apiParam {Number} day 出生日（真太阳时）
 * @apiParam {Number} hour 出生时（真太阳时，24小时制）
 * @apiParam {Number} [minute=0] 出生分（真太阳时）
 * @apiParam {String} gender 性别，"男"或"女"
 *
 * @apiExample {json} 请求示例:
 *  {
 *    "year": 1990,
 *    "month": 10,
 *    "day": 1,
 *    "hour": 12,
 *    "minute": 30,
 *    "gender": "男"
 *  }
 *
 * @apiSuccess {Number} code 状态码
 * @apiSuccess {String} message 状态信息
 * @apiSuccess {Object} data 八字数据
 * @apiSuccess {String} data.yearColumn 年柱
 * @apiSuccess {String} data.monthColumn 月柱
 * @apiSuccess {String} data.dayColumn 日柱
 * @apiSuccess {String} data.timeColumn 时柱
 * @apiSuccess {String} data.fullBazi 完整八字
 * @apiSuccess {Object} data.dayun 大运信息
 * @apiSuccess {Number} data.dayun.startYear 起运年数
 * @apiSuccess {Number} data.dayun.startMonth 起运月数
 * @apiSuccess {Number} data.dayun.startDay 起运天数
 * @apiSuccess {Array} data.dayun.list 大运列表
 * @apiSuccess {Array} data.liunian 近期流年
 *
 * @apiSuccessExample {json} 成功响应:
 *  HTTP/1.1 200 OK
 *  {
 *    "code": 200,
 *    "message": "八字计算成功",
 *    "data": {
 *      "yearColumn": "庚午",
 *      "monthColumn": "戊戌",
 *      "dayColumn": "丙申",
 *      "timeColumn": "戊午",
 *      "fullBazi": "庚午 戊戌 丙申 戊午",
 *      "dayun": {
 *        "startYear": 8,
 *        "startMonth": 3,
 *        "startDay": 20,
 *        "list": [
 *          {
 *            "sequence": 1,
 *            "age": 9,
 *            "endAge": 19,
 *            "ganZhi": "丁酉",
 *            "startYear": 1999
 *          },
 *          // 更多大运...
 *        ]
 *      },
 *      "liunian": [
 *        {
 *          "year": 2023,
 *          "ganZhi": "癸卯"
 *        },
 *        // 更多流年...
 *      ],
 *      "solar": "公历1990年10月1日12时30分",
 *      "lunar": "一九九〇年八月十三日午时"
 *    }
 *  }
 */
// 添加八字计算API接口
router.post('/calculate-bazi', async (req, res) => {
  const userInfo = req.body;
  // 验证必要字段
  const requiredFields = ['year', 'month', 'day', 'hour', 'gender'];
  const missingFields = requiredFields.filter(field => !userInfo[field]);
  
  if (missingFields.length > 0) {
    return res.json({
      code: 400,
      message: `缺少必要参数: ${missingFields.join(', ')}`,
      error: true
    });
  }
  
  try {
    // 确保经度信息正确传递
    if (userInfo.longitude) {
      // 确保经度是数字类型
      userInfo.longitude = parseFloat(userInfo.longitude);
    } else {
      logger.info('【2. 后台接收】警告: 请求中没有经度信息，将使用默认值120');
    }
    
    // 直接调用Python八字计算，跳过baziUtils中间层
    logger.info('【3. 调用Python】开始计算八字');
    const baziInfo = await pythonBazi.calculateBaziWithPython(userInfo);
    
    logger.info('【4. 计算结果】八字计算成功，返回结果');
    res.json({
      code: 200,
      message: "八字计算成功",
      data: baziInfo
    });
  } catch (error) {
    logger.error('【错误】八字计算异常:', { error: error.message, stack: error.stack });
    res.json({
      code: 500,
      message: error.message || "八字计算失败",
      error: true
    });
  }
});

/**
 * @api {post} /api/v1/analyze 分析八字
 * @apiName AnalyzeBazi
 * @apiGroup Bazi
 * @apiDescription 分析用户的八字信息，包括四柱、大运和流年（注意：传入的时间必须是已经计算好的真太阳时）
 *
 * @apiParam {Number} year 出生年（真太阳时）
 * @apiParam {Number} month 出生月（真太阳时）
 * @apiParam {Number} day 出生日（真太阳时）
 * @apiParam {Number} hour 出生时（真太阳时，24小时制）
 * @apiParam {Number} [minute=0] 出生分（真太阳时）
 * @apiParam {String} gender 性别，"男"或"女"
 *
 * @apiExample {json} 请求示例:
 *  {
 *    "year": 1990,
 *    "month": 10,
 *    "day": 1,
 *    "hour": 12,
 *    "minute": 30,
 *    "gender": "男"
 *  }
 *
 * @apiSuccess {Number} code 状态码
 * @apiSuccess {String} message 状态信息
 * @apiSuccess {Object} data 分析结果
 * @apiSuccess {String} data.analysis 分析结果描述
 *
 * @apiSuccessExample {json} 成功响应:
 *  HTTP/1.1 200 OK
 *  {
 *    "code": 200,
 *    "message": "分析成功",
 *    "data": {
 *      "analysis": "这是一个详细的分析结果描述，包括四柱、大运和流年等信息。"
 *    }
 *  }
 */
// Chat-Model (V3-model) 分析接口 
router.post('/analyze', async (req, res) => {
  const userInfo = req.body;
  
  // 生成请求ID，用于日志跟踪
  const submissionId = userInfo.submissionId;
  // 提取 infoHash 用于查找临时文件和请求锁
  const infoHash = userInfo.infoHash;
  
  logger.info(`【1. 用户输入】收到八字分析请求 [ID: ${submissionId}]`, { userInfo });
  
  // 检查该infoHash是否已经在处理中
  if (infoHash && requestLock.isLocked(infoHash)) {
    logger.info(`【请求重复】infoHash=${infoHash}的请求已在处理中，忽略此次请求`);
    return res.json({
      code: 40001,
      message: "分析请求已受理",
      data: {
        infoHash: infoHash,
        status: "processing",
        basicAnalysis: "您的分析请求正在处理中，请稍后刷新查看结果",
        detailedAdvice: "系统正在为您精心分析，请稍候..."
      }
    });
  }
  
  // 如果有infoHash，检查临时文件是否已存在
  if (infoHash) {
    const tempDir = path.join(process.cwd(), 'temp');
    const resultFile = path.join(tempDir, `${infoHash}.result.json`);
    
    // 如果文件已存在，检查是否包含基础分析结果
    if (fs.existsSync(resultFile)) {
      try {
        const fileContent = fs.readFileSync(resultFile, 'utf8');
        const result = JSON.parse(fileContent);
        
        // 如果已有基础分析结果，直接返回
        if (result.basicAnalysis && result.success) {
          logger.info(`【临时文件】找到infoHash=${infoHash}的基础分析结果，直接返回`);
          return res.json({
            code: 200,
            message: "分析成功",
            data: {
              basicAnalysis: result.basicAnalysis,
              detailedAdvice: result.detailedAdvice || "",
            }
          });
        }
      } catch (error) {
        logger.warn(`【临时文件】读取失败: ${error.message}`);
        // 继续处理，重新分析
      }
    }
  }
  
  // 尝试锁定请求
  if (infoHash && !requestLock.tryLock(infoHash)) {
    logger.info(`【请求重复】infoHash=${infoHash}的请求已在处理中，忽略此次请求`);
    return res.json({
      code: 40001,
      message: "分析请求已受理",
      data: {
        infoHash: infoHash,
        status: "processing",
        basicAnalysis: "您的分析请求正在处理中，请稍后刷新查看结果",
        detailedAdvice: "系统正在为您精心分析，请稍候..."
      }
    });
  }
  
  try {
    // 验证必要字段
    const requiredFields = ['year', 'month', 'day', 'hour', 'gender'];
    const missingFields = requiredFields.filter(field => !userInfo[field]);
    
    if (missingFields.length > 0) {
      logger.warn(`【错误】请求缺少必要参数 [ID: ${submissionId}]`, { missingFields });
      // 释放锁
      if (infoHash) requestLock.releaseLock(infoHash);
      return res.json({
        code: 400,
        message: `缺少必要参数: ${missingFields.join(', ')}`,
        error: true
      });
    }
    
    try {
      // 确保经度信息正确传递
      if (userInfo.longitude) {
        userInfo.longitude = parseFloat(userInfo.longitude);
      } else {
        logger.info(`【2. 后台接收】警告: 请求中没有经度信息 [ID: ${submissionId}]，将使用默认值120`);
      }
      
      // 1. 直接调用Python八字计算，跳过baziUtils中间层
      const baziInfo = await pythonBazi.calculateBaziWithPython(userInfo);
      logger.info(`【3. 调用Python】八字计算完成 [ID: ${submissionId}]`);
      
      // 2. 生成基础分析提示词
      const basicPrompt = await deepseekService.generatePrompt({...userInfo, ...baziInfo});
      const guidancePart = `请基于此人八字命盘，分析此人的天赋性格，体貌特征，最适合从事哪些行业以及适合的岗位，适合在哪里发展，事业的大小，身体健康状况，再着重分析大运大富大贵的可能性，如何发挥最好的天赋和潜能。`;
      const fullPrompt = `${basicPrompt}\n${guidancePart}`;

      // 3. 调用DeepSeek API进行基础分析
      const basicResponse = await deepseekService.makeRequest(fullPrompt);
      logger.info(`【4. 基础分析完成 [ID: ${submissionId}]`);

      if (!basicResponse.success) {
        logger.error(`【错误】DeepSeek基础分析失败 [ID: ${submissionId}]`, { error: basicResponse.error });
        // 释放锁
        if (infoHash) requestLock.releaseLock(infoHash);
        return res.json({
          code: 500,
          message: basicResponse.error || "分析失败，请稍后再试",
          error: true
        });
      }
      
      // 处理响应结果
      let basicAnalysis = basicResponse.analysis;
      
      // 限制返回的分析结果长度，避免过长导致微信小程序处理问题
      if (basicAnalysis && basicAnalysis.length > 8000) {
        logger.info(`【4. DeepSeek】基础分析结果过长(${basicAnalysis.length}字符)，截断至8000字符`);
        basicAnalysis = basicAnalysis.substring(0, 8000) + "...(内容过长已截断)";
      }
      
      // 如果有infoHash，将基础分析结果保存到临时文件
      if (infoHash) {
        try {
          const tempDir = path.join(process.cwd(), 'temp');
          
          // 确保临时目录存在
          if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
          }
          
          const resultFile = path.join(tempDir, `${infoHash}.result.json`);
          // 准备要更新的数据
          let updateData = {
            success: true,
            infoHash: infoHash,
            basicAnalysis: basicAnalysis,
            lastUpdateTime: Date.now()
          };
          
          // 如果有submissionId，也保存它
          if (submissionId) {
            updateData.submissionId = submissionId;
          }
          
          // 如果文件已存在，读取现有内容并合并
          let existingData = {};
          if (fs.existsSync(resultFile)) {
            try {
              existingData = JSON.parse(fs.readFileSync(resultFile, 'utf8'));
              logger.info(`【临时文件】读取已有临时文件 [infoHash: ${infoHash}]`);
            } catch (e) {
              logger.warn(`【临时文件】解析已有临时文件失败: ${e.message}，将创建新文件`);
            }
          }
          
          // 合并现有数据和新数据，保留推理分析相关字段
          const mergedData = {
            ...existingData,
            ...updateData
          };
          
          // 写入文件
          fs.writeFileSync(resultFile, JSON.stringify(mergedData, null, 2), 'utf8');
          logger.info(`【临时文件】基础分析结果已写入/更新临时文件: ${resultFile}`);
        } catch (error) {
          logger.error(`【临时文件】写入失败: ${error.message}`);
          // 继续处理，不影响正常响应
        }
      }
      
      // 释放锁
      if (infoHash) requestLock.releaseLock(infoHash);
      
      // 修改响应格式，确保与前端期望一致
      return res.json({
        code: 200,
        message: "分析成功",
        data: {
          basicAnalysis: basicAnalysis,
          detailedAdvice: "", // 保留字段但返回空字符串
        }
      });
    } catch (innerError) {
      logger.error(`【错误】内部处理异常 [ID: ${submissionId}]`, { error: innerError.message, stack: innerError.stack });
      // 释放锁
      if (infoHash) requestLock.releaseLock(infoHash);
      throw innerError; // 向外层传递错误
    }
  } catch (error) {
    logger.error(`【错误】分析异常 [ID: ${submissionId}]`, { error: error.message, stack: error.stack });
    
    // 释放锁
    if (infoHash) requestLock.releaseLock(infoHash);
    
    // 总是返回正常状态码，但在响应中包含错误信息
    // 这可以避免微信小程序因为500错误而无法处理响应
    return res.json({
      code: 500,
      message: error.message || "服务器内部错误",
      error: true
    });
  }
});


/**
 * @api {post} /api/v1/reasoner 使用推理模型分析八字（非流式）
 * @apiName ReasonerAnalyzeBazi
 * @apiGroup Bazi
 * @apiDescription 使用推理模型分析用户的八字信息，非流式输出
 *
 * @apiParam {Number} year 出生年
 * @apiParam {Number} month 出生月
 * @apiParam {Number} day 出生日
 * @apiParam {Number} hour 出生时（24小时制）
 * @apiParam {Number} [minute=0] 出生分
 * @apiParam {String} gender 性别，"男"或"女"
 *
 * @apiExample {json} 请求示例:
 *  {
 *    "year": 1990,
 *    "month": 10,
 *    "day": 1,
 *    "hour": 12,
 *    "minute": 30,
 *    "gender": "男"
 *  }
 *
 * @apiSuccess {Number} code 状态码
 * @apiSuccess {String} message 状态信息
 * @apiSuccess {Object} data 分析结果
 * @apiSuccess {String} data.reasoningContent 推理过程
 * @apiSuccess {String} data.content 最终结论
 */
router.post('/reasoner', async (req, res) => {
  const userInfo = req.body;
  
  // 生成请求ID，用于日志跟踪
  const submissionId = userInfo.submissionId;
  // 提取 infoHash 用于查找临时文件
  const infoHash = userInfo.infoHash;
  
  logger.info(`【1. 用户输入】收到推理模型分析请求 [ID: ${submissionId}]`, { userInfo });
  
  // 检查该infoHash是否已经在处理中
  if (infoHash && requestLock.isLocked(infoHash)) {
    logger.info(`【请求重复】infoHash=${infoHash}的请求已在处理中，忽略此次请求`);
    return res.json({
      code: 40001,
      message: "分析请求已受理",
      data: {
        infoHash: infoHash,
        status: "processing",
        reasoningContent: "您的分析请求正在处理中，请稍后刷新查看结果",
        content: "系统正在为您精心分析，请稍候..."
      }
    });
  }
  
  if (infoHash) {
    // 检查是否已存在对应的临时文件
    const tempDir = path.join(process.cwd(), 'temp');
    const resultFile = path.join(tempDir, `${infoHash}.result.json`);
    
    // 如果存在对应的临时文件，直接读取并返回结果
    if (fs.existsSync(resultFile)) {
      try {
        const fileContent = fs.readFileSync(resultFile, 'utf8');
        const result = JSON.parse(fileContent);
        
        // 验证文件内容是否有效
        if (result.success && result.firstStage) {
          logger.info(`【2. 临时文件】文件内容有效，直接返回结果`);
          
          // 从临时文件中提取 firstStage 内容
          const { content, reasoningContent } = result.firstStage;
          
          return res.json({
            code: 200,
            message: "分析成功",
            data: {
              reasoningContent: reasoningContent,
              content: content,
              hasSecondStage: true,  // 标记有第二轮分析
            }
          });
        } else {
          logger.info(`【2. 临时文件】文件内容格式无效，将重新分析`);
          // 继续执行常规分析流程
        }
      } catch (error) {
        logger.error(`【错误】读取临时文件失败 [ID: ${submissionId}]`, { error: error.message, stack: error.stack });
        // 继续执行常规分析流程
      }
    } else {
      logger.info(`【2. 临时文件】未找到对应的分析结果文件，将进行新分析`);
    }
  }
  
  // 尝试锁定请求，如果请求已经在处理中，返回处理中的消息
  if (infoHash && !requestLock.tryLock(infoHash)) {
    logger.info(`【请求重复】infoHash=${infoHash}的请求已在处理中，忽略此次请求`);
    return res.json({
      code: 40001,
      message: "分析请求已受理",
      data: {
        infoHash: infoHash,
        status: "processing",
        reasoningContent: "您的分析请求正在处理中，请稍后刷新查看结果",
        content: "系统正在为您精心分析，请稍候..."
      }
    });
  }
  
  try {
    try {
      // 确保经度信息正确传递
      if (userInfo.longitude) {
        // 确保经度是数字类型
        userInfo.longitude = parseFloat(userInfo.longitude);
      } else {
        logger.info(`【3. 后台接收】警告: 请求中没有经度信息 [ID: ${submissionId}]，将使用默认值120`);
      }
      
      // 1. 直接调用Python八字计算，跳过baziUtils中间层
      const baziInfo = await pythonBazi.calculateBaziWithPython(userInfo);
      logger.info(`【4. 调用Python】八字计算完成 [ID: ${submissionId}]`);
      
      // 组合用户输入信息和八字计算结果
      const analysisData = { ...userInfo, ...baziInfo };

      // 调用两轮分析方法
      const result = await deepseekService.twoStageAnalysis(analysisData);
      
      if (!result.success) {
        logger.error(`【错误】推理模型分析失败 [ID: ${submissionId}]`, { error: result.error });
        // 释放锁定
        if (infoHash) requestLock.releaseLock(infoHash);
        return res.json({
          code: 500,
          message: result.error || `推理模型分析失败`,
          error: true
        });
      }
      
      logger.info(`【5. DeepSeek推理】推理模型第一轮分析完成 [ID: ${submissionId}]`);
      
      // 限制返回的分析结果长度，避免过长导致微信小程序处理问题
      let reasoningContent = result.firstStage.reasoningContent;
      let content = result.firstStage.content;
      
      if (reasoningContent && reasoningContent.length > 8000) {
        logger.info(`【5. DeepSeek推理】推理过程过长(${reasoningContent.length}字符)，截断至8000字符`);
        reasoningContent = reasoningContent.substring(0, 8000) + "...(内容过长已截断)";
      }
      
      if (content && content.length > 8000) {
        logger.info(`【5. DeepSeek推理】最终结论过长(${content.length}字符)，截断至8000字符`);
        content = content.substring(0, 8000) + "...(内容过长已截断)";
      }
      
      // 释放锁定
      if (infoHash) requestLock.releaseLock(infoHash);
      
      // 返回结果
      return res.json({
        code: 200,
        message: "分析成功",
        data: {
          reasoningContent: reasoningContent,
          content: content,
          hasSecondStage: true,  // 标记有第二轮分析
        }
      });
      
    } catch (error) {
      logger.error(`【错误】内部处理异常 [ID: ${submissionId}]`, { error: error.message, stack: error.stack });
      throw error; // 向外层传递错误
    }
  } catch (error) {
    logger.error(`【错误】推理模型分析异常 [ID: ${submissionId}]`, { error: error.message, stack: error.stack });
    
    // 释放锁定
    if (infoHash) requestLock.releaseLock(infoHash);
    
    // 返回标准化的错误响应
    return res.json({
      code: 500,
      message: error.message || "服务器内部错误",
      error: true
    });
  }
});

// 添加新的API端点，用于获取第二轮分析结果
router.post('/reasoner/second-stage', async (req, res) => {
  try {
    const { infoHash } = req.body;
    
    if (!infoHash) {
      return res.json({
        success: false,
        code: 'MISSING_HASH',
        message: '缺少必要的infoHash字段'
      });
    }
    
    // 直接调用服务获取第二轮分析结果
    const result = await deepseekService.getSecondStageAnalysis({ infoHash });
    
    // 返回结果，保持与DeepSeek服务相同的格式和状态码
    res.json(result);
    
  } catch (error) {
    res.json({
      success: false,
      code: 'UNKNOWN_ERROR',
      message: error.message || '获取第二轮分析结果失败'
    });
  }
});



module.exports = router;