/**
 * 地理位置服务
 * 提供地址到经纬度的查询功能
 * 优先使用本地数据，如果没有则调用腾讯地图API并更新本地数据
 */
require('dotenv').config();
const fs = require('fs');
const axios = require('axios'); // 使用axios进行HTTP请求
const path = require('path');
const logger = require('../utils/logger');
const config = require('../config');
// 引入本地地理位置数据，允许修改，因此用 let
let { regionGeoMap } = require('../data/geo-data');

// 定义geo-data.js文件路径
const GEO_DATA_FILE_PATH = path.resolve(__dirname, '../data/geo-data.js');

// 实例化腾讯地图API核心类
// Fallback data - Chengdu Tianfu Square
const FALLBACK_LATITUDE = 30;
const FALLBACK_LONGITUDE = 120;

/**
 * 根据地区完整名称查询经纬度（本地数据）
 * @param {String} fullRegionName 完整地区名称，如"四川省成都市"
 * @returns {Object|null} 包含经纬度的对象或null
 */
function getGeoByRegion(fullRegionName) {
  if (regionGeoMap[fullRegionName]) {
    logger.debug(`[Geo] 从本地数据查询到地区经纬度: ${fullRegionName}`);
    return regionGeoMap[fullRegionName];
  }
  return null;
}

/**
 * 将地理位置数据写入到geo-data.js文件
 * @returns {Boolean} 是否写入成功
 */
function saveGeoDataToFile() {
  try {
    const fileContent = `const regionGeoMap = ${JSON.stringify(regionGeoMap, null, 2)};\n\nmodule.exports = {\n  regionGeoMap\n};`;
    fs.writeFileSync(GEO_DATA_FILE_PATH, fileContent, 'utf8');
    logger.info(`[Geo] 成功将地理位置数据写入到文件: ${GEO_DATA_FILE_PATH}`);
    return true;
  } catch (error) {
    logger.error(`[Geo] 将地理位置数据写入到文件时出错:`, error);
    return false;
  }
}

/**
 * 添加新的地区经纬度数据到本地映射表并持久化
 * @param {String} regionName 地区名称
 * @param {Number} latitude 纬度
 * @param {Number} longitude 经度
 * @returns {Boolean} 是否添加成功
 */
function addRegionGeo(regionName, latitude, longitude) {
  if (regionName && typeof latitude === 'number' && typeof longitude === 'number') {
    regionGeoMap[regionName] = { latitude, longitude };
    const saveResult = saveGeoDataToFile();
    if (saveResult) {
      logger.info(`[Geo] 成功添加地区经纬度并持久化: ${regionName}`);
    } else {
      logger.warn(`[Geo] 添加地区经纬度成功，但持久化失败: ${regionName}`);
    }
    return true;
  }
  logger.warn(`[Geo] 添加地区经纬度参数无效: ${regionName}, ${latitude}, ${longitude}`);
  return false;
}

/**
 * 通过腾讯地图API获取地址的经纬度
 * @param {String} address 地址字符串
 * @returns {Promise<Object|null>} 包含经纬度的对象或null
 */
async function getGeoByTencentMap(address) {
  logger.info(`[Geo] 使用WebService API查询腾讯地图: ${address}`);
  const tencentMapKey = config.tencentMapKey || process.env.TENCENT_MAP_KEY;
  if (!tencentMapKey) {
    logger.error('[Geo] 腾讯地图API Key未配置. Using fallback.');
    return { latitude: FALLBACK_LATITUDE, longitude: FALLBACK_LONGITUDE, fallbackUsed: true };
  }

  const TENCENT_MAP_API_URL = 'https://apis.map.qq.com/ws/geocoder/v1/';
  try {
    const response = await axios.get(TENCENT_MAP_API_URL, {
      params: {
        address: address,
        key: tencentMapKey
      }
    });

    if (response.data && response.data.status === 0 && response.data.result && response.data.result.location) {
      const location = response.data.result.location;
      logger.info(`[Geo] 腾讯地图WebService API成功返回: ${JSON.stringify(location)}`);
      return {
        latitude: location.lat,
        longitude: location.lng,
        fallbackUsed: false
      };
    } else {
      logger.warn(`[Geo] 腾讯地图WebService API返回无效数据或错误 for address ${address}: ${response.data.message || '未知错误'} - Status: ${response.data.status}. Using fallback.`);
      return { latitude: FALLBACK_LATITUDE, longitude: FALLBACK_LONGITUDE, fallbackUsed: true };
    }
  } catch (error) {
    logger.error(`[Geo] 腾讯地图WebService API请求失败 for address ${address}:`, error.isAxiosError ? error.message : error);
    logger.warn(`[Geo] API call failed for address: ${address}. Using fallback.`);
    return { latitude: FALLBACK_LATITUDE, longitude: FALLBACK_LONGITUDE, fallbackUsed: true };
  }
}

/**
 * 获取地址的经纬度信息
 * 优先使用本地数据，如果没有则调用腾讯地图API并更新本地数据
 * @param {String} address 地址字符串
 * @returns {Promise<Object|null>} 包含经纬度的对象或null
 */
async function getAddressLocation(address) {

  const formattedAddress = address.trim();

  const localGeo = getGeoByRegion(formattedAddress);
  if (localGeo) {
    return localGeo;
  }

  logger.info(`[Geo] 本地无此地址，调用腾讯地图API: ${formattedAddress}`);
  const remoteGeo = await getGeoByTencentMap(formattedAddress);

  // Ensure remoteGeo is not null before accessing its properties
  if (remoteGeo && !remoteGeo.fallbackUsed) {
    addRegionGeo(formattedAddress, remoteGeo.latitude, remoteGeo.longitude);
    return { latitude: remoteGeo.latitude, longitude: remoteGeo.longitude };
  } else if (remoteGeo && remoteGeo.fallbackUsed) {
    // If fallback was used by getGeoByTencentMap, return that fallback
    return { latitude: remoteGeo.latitude, longitude: remoteGeo.longitude };
  }

  return { latitude: FALLBACK_LATITUDE, longitude: FALLBACK_LONGITUDE };
}
module.exports = {
  getAddressLocation
};
