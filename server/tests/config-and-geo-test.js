/**
 * u6d4bu8bd5u914du7f6eu548cu5730u7406u4f4du7f6eu670du52a1
 * u5148u68c0u67e5u914du7f6eu52a0u8f7du662fu5426u6b63u786euff0cu7136u540eu6d4bu8bd5u5730u7406u7f16u7801u529fu80fd
 */

// u76f4u63a5u52a0u8f7d dotenv
require('dotenv').config({ path: 'server/.env' });

// u68c0u67e5u73afu5883u53d8u91cf
console.log('==== u68c0u67e5u73afu5883u53d8u91cfu548cu914du7f6e ====');
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'u672au8bbeu7f6e'}`);

// u68c0u67e5u817eu8bafu5730u56feu5bc6u94a5u662fu5426u5b58u5728
const hasTencentMapKey = !!process.env.TENCENT_MAP_KEY;
console.log(`TENCENT_MAP_KEY u662fu5426u5b58u5728: ${hasTencentMapKey ? 'u2705 u5b58u5728' : 'u274c u4e0du5b58u5728'}`);

if (hasTencentMapKey) {
  console.log(`TENCENT_MAP_KEY u957fu5ea6: ${process.env.TENCENT_MAP_KEY.length} u5b57u7b26`);
  // u53eau663eu793au524du51e0u4e2au5b57u7b26uff0cu4fddu62a4u5bc6u94a5u5b89u5168
  console.log(`TENCENT_MAP_KEY u524du4e24u4e2au5b57u7b26: ${process.env.TENCENT_MAP_KEY.substring(0, 2)}****`);
} else {
  console.log('u2757 u8bf7u68c0u67e5 .env u6587u4ef6u4e2du662fu5426u6b63u786eu8bbeu7f6eu4e86 TENCENT_MAP_KEY');
  console.log('u6ce8u610f: .env u6587u4ef6u5e94u8be5u4f4du4e8eu670du52a1u5668u76eeu5f55u4e0b (/server/.env)');
  process.exit(1); // u5982u679cu6ca1u6709u5bc6u94a5uff0cu76f4u63a5u9000u51fa
}

// u68c0u67e5u914du7f6eu6a21u5757
const config = require('../config');
console.log('\n==== u68c0u67e5u914du7f6eu6a21u5757 ====');
console.log(`config u5bf9u8c61u7c7bu578b: ${typeof config}`);
console.log(`config u5c5eu6027: ${Object.keys(config).join(', ')}`);

const hasConfigKey = !!config.tencentMapKey;
console.log(`config.tencentMapKey u662fu5426u5b58u5728: ${hasConfigKey ? 'u2705 u5b58u5728' : 'u274c u4e0du5b58u5728'}`);

if (hasConfigKey) {
  console.log(`config.tencentMapKey u957fu5ea6: ${config.tencentMapKey.length} u5b57u7b26`);
  console.log(`config.tencentMapKey u524du4e24u4e2au5b57u7b26: ${config.tencentMapKey.substring(0, 2)}****`);
} else {
  console.log('u2757 config.js u4e2du6ca1u6709u6b63u786eu5bfcu51fa tencentMapKey');
  console.log('u8bf7u68c0u67e5 server/config/index.js u6587u4ef6u4e2du662fu5426u6b63u786eu5bfcu51fau4e86 tencentMapKey');
  process.exit(1); // u5982u679cu914du7f6eu4e2du6ca1u6709u5bc6u94a5uff0cu76f4u63a5u9000u51fa
}

// u65e5u5fd7u5de5u5177
const logger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  debug: console.log
};

// u6d4bu8bd5u5730u5740u6570u7ec4
const testAddresses = [
  'u56dbu5dddu7701u6210u90fdu5e02', // u7b80u5355u5730u5740
  'u5317u4eacu5e02u6d77u6dc0u533a',      // u53e6u4e00u4e2au5730u5740
];

// u521bu5efau4e00u4e2au6700u5c0fu5316u7248u672cu7684u6d4bu8bd5u6a21u5757
// u8fd9u91ccu6211u4eecu76f4u63a5u4f7fu7528QQMapWXu800cu4e0du8c03u7528geo-serviceu4ee5u964du4f4eu590du6742u6027
const QQMapWX = require('../libs/qqmap-wx-jssdk1.2/qqmap-wx-jssdk.js');

// u6d4bu8bd5u521du59cbu5316SDK
console.log('\n==== u521du59cbu5316u817eu8bafu5730u56feSDK ====');
try {
  const qqmapsdk = new QQMapWX({
    key: config.tencentMapKey
  });
  console.log('u2705 SDKu521du59cbu5316u6210u529f');
  
  // u6d4bu8bd5u5730u7406u7f16u7801u529fu80fd
  console.log('\n==== u6d4bu8bd5u5730u7406u7f16u7801u529fu80fd ====');
  
  // u5f15u5165u5c0fu7a0bu5e8fu517cu5bb9u5c42
  global.wx = {
    request: function(options) {
      // u5c0fu7a0bu5e8fu8bf7u6c42u7684u6a21u62dfu5b9eu73b0
      console.log('u5c1du8bd5u53d1u8d77u7f51u7edcu8bf7u6c42uff0cu4f46u6b64u6d4bu8bd5u73afu5883u4e0du652fu6301u771fu5b9eu7f51u7edcu8c03u7528');
      console.log(`URL: ${options.url}`);
      
      // u8fd4u56deu6a21u62dfu6570u636euff0cu6b63u5e38u60c5u51b5u4e0bu4f1au5931u8d25uff0cu56e0u4e3au8fd9u53eau662fu4e00u4e2au6a21u62df
      if (options.fail) {
        options.fail({
          errMsg: 'request:fail u6d4bu8bd5u73afu5883u4e0bu65e0u6cd5u53d1u8d77u771fu5b9eu7f51u7edcu8bf7u6c42'
        });
      }
    }
  };
  
  // u5c1du8bd5u8c03u7528SDKu7684geocoderu65b9u6cd5
  const address = testAddresses[0];
  console.log(`u6d4bu8bd5u5730u5740: ${address}`);
  
  qqmapsdk.geocoder({
    address: address,
    success: function(res) {
      console.log('u2705 u8c03u7528geocoderu6210u529fuff0cu4f46u5728u975eu5c0fu7a0bu5e8fu73afu5883u4e0bu5b9eu9645u4e0du4f1au5230u8fbeu8fd9u91cc');
      console.log(res);
    },
    fail: function(error) {
      console.log('u274c u8c03u7528geocoderu5931u8d25uff08u5728Node.jsu73afu5883u4e2du8fd9u662fu9884u671fu7684uff09');
      console.log(error);
    }
  });
  
  console.log('\nu6ce8u610f: u5c0fu7a0bu5e8f SDK u4e0du80fdu5728Node.jsu73afu5883u76f4u63a5u8fd0u884cuff0cu8fd9u9700u8981u6a21u62dfu5c0fu7a0bu5e8fu73afu5883u624du80fdu6b63u5e38u5de5u4f5cu3002');
  console.log('u672cu6d4bu8bd5u53eau80fdu9a8cu8bc1u73afu5883u53d8u91cfu548cu914du7f6eu662fu5426u6b63u786eu52a0u8f7du3002');
  console.log('u8981u8fdbu884cu5b8cu6574u6d4bu8bd5uff0cu9700u8981u5728u5c0fu7a0bu5e8fu73afu5883u4e2du8fd0u884cu6216u5c06SDKu8fdbu884cu9002u914du3002');
  
} catch (error) {
  console.error(`u274c SDKu521du59cbu5316u5931u8d25: ${error.message}`);
  console.log('u8bf7u68c0u67e5u914du7f6eu662fu5426u6b63u786eu4ee5u53cau5bc6u94a5u662fu5426u6709u6548u3002');
}

console.log('\n==== u6d4bu8bd5u5b8cu6210 ====');
