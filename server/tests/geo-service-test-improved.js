/**
 * u6d4bu8bd5u6587u4ef6 - u5730u7406u4f4du7f6eu670du52a1u6d4bu8bd5 (u6539u8fdbu7248)
 * u9996u5148u52a0u8f7du73afu5883u53d8u91cfuff0cu7136u540eu6d4bu8bd5u5730u5740u67e5u8be2u7ecfu7eacu5ea6u529fu80fd
 */

// u5148u52a0u8f7du73afu5883u53d8u91cf
require('dotenv').config({ path: 'server/.env' });

// u68c0u67e5u5bc6u94a5u662fu5426u5b58u5728
if (!process.env.TENCENT_MAP_KEY) {
  console.error('u9519u8bef: u672au8bbeu7f6e TENCENT_MAP_KEY u73afu5883u53d8u91cf');
  console.log('u8bf7u786eu4fdd server/.env u6587u4ef6u4e2du5b9au4e49u4e86 TENCENT_MAP_KEY=u4f60u7684u817eu8bafu5730u56feu5bc6u94a5');
  process.exit(1);
}

// u4e3au4e86u907fu514du5728u5bfcu5165u65f6u5c31u7f3au5931u5bc6u94a5uff0cu624bu52a8u4feeu6539u914du7f6eu6587u4ef6
// u8fd9u786eu4fddu5728u8bfbu53d6u914du7f6eu6587u4ef6u524du5df2u7ecfu8bbeu7f6eu4e86u73afu5883u53d8u91cf
const config = require('../config');
if (!config.tencentMapKey) {
  console.error('u9519u8bef: config.tencentMapKey u4e3au7a7auff0cu914du7f6eu6587u4ef6u672au6b63u786eu5bfcu51fa tencentMapKey');
  process.exit(1);
}

// u5f15u5165u5176u5b83u6240u9700u6a21u5757
const logger = require('../utils/logger');

// u73b0u5728u5f15u5165u5730u7406u670du52a1
// u5728 Node.js u73afu5883u4e2duff0cu817eu8bafu5730u56fe SDK u53efu80fdu4f1au5931u8d25uff0cu56e0u4e3au5b83u4f9du8d56u5c0fu7a0bu5e8f API
// u4f46u6211u4eecu7684u670du52a1u5df2u7ecfu5b9eu73b0u4e86 fallback u673au5236uff0cu6240u4ee5u5e94u8be5u80fdu5de5u4f5c
let geoService;
try {
  geoService = require('../services/geo-service');
  console.log('u2705 u6210u529fu5bfcu5165 geo-service u6a21u5757');
} catch (error) {
  console.error('u5bfcu5165 geo-service u6a21u5757u65f6u51fau9519:', error);
  process.exit(1);
}

// u6d4bu8bd5u5730u5740u6570u7ec4
const testAddresses = [
  'u56dbu5dddu7701u6210u90fdu5e02u6b66u4fafu533a', // u5b8cu6574u5730u5740
  'u5317u4eacu5e02u6d77u6dc0u533a',      // u53e6u4e00u4e2au5b8cu6574u5730u5740
  'u4e0au6d77u6d66u4e1cu65b0u533a',       // u7701u7565u7701u4efd
  'u5e7fu5ddeu5929u6cb3',          // u7701u7565u7701u4efdu548cu5e02u540eu7f00
  '',                  // u7a7au5b57u7b26u4e32uff0cu6d4bu8bd5u8fb9u754cu60c5u51b5
  'u4e0du5b58u5728u7684u5730u5740XYZABC' // u53efu80fdu4e0du5b58u5728u7684u5730u5740uff0cu6d4bu8bd5fallback
];

/**
 * u6d4bu8bd5u5730u5740u8f6cu7ecfu7eacu5ea6u529fu80fd
 * @param {String} address u9700u8981u6d4bu8bd5u7684u5730u5740
 * @returns {Promise<void>}
 */
async function testAddressGeocoding(address) {
  try {
    console.log(`\nu6b63u5728u6d4bu8bd5u5730u5740: "${address}"`); 
    const startTime = Date.now();
    
    // u8c03u7528u5730u7406u670du52a1u8f6cu6362u5730u5740
    console.log('u8c03u7528 getAddressLocation...');
    const result = await geoService.getAddressLocation(address);
    const endTime = Date.now();
    
    if (!result) {
      console.log(`u274c u67e5u8be2u5931u8d25: u8fd4u56denullu6216undefined`);
      return;
    }
    
    const { latitude, longitude } = result;
    console.log(`u2705 u67e5u8be2u6210u529f: u7eacu5ea6=${latitude}, u7ecfu5ea6=${longitude}`);
    console.log(`u23f1ufe0f u8017u65f6: ${endTime - startTime}ms`);
    
    // u9a8cu8bc1u7ed3u679cu662fu5426u5408u7406
    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      console.log(`u26a0ufe0f u8b66u544a: u8fd4u56deu7684u7ecfu7eacu5ea6u4e0du662fu6570u5b57u7c7bu578b`);
    }
    
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      console.log(`u26a0ufe0f u8b66u544a: u7ecfu7eacu5ea6u8d85u51fau6709u6548u8303u56f4 (u7eacu5ea6:-90~90, u7ecfu5ea6:-180~180)`);
    }
    
  } catch (error) {
    console.error(`u274c u6d4bu8bd5u51fau9519: ${error.message}`);
  }
}

/**
 * u8fd0u884cu6240u6709u6d4bu8bd5
 */
async function runTests() {
  console.log('==== u5730u7406u4f4du7f6eu670du52a1u6d4bu8bd5u5f00u59cb ====');
  console.log('u6ce8u610f: u5728Node.jsu73afu5883u4e2du8fd0u884cu817eu8bafu5730u56feSDKu53efu80fdu4f1au6709u95eeu9898uff0cu4f46u6211u4eecu4f7fu7528u4e86fallbacku673au5236');
  
  // u987au5e8fu6d4bu8bd5u6240u6709u5730u5740
  for (const address of testAddresses) {
    await testAddressGeocoding(address);
  }
  
  console.log('\n==== u5730u7406u4f4du7f6eu670du52a1u6d4bu8bd5u5b8cu6210 ====');
}

// u786eu4fddu6211u4eecu6709wxu5bf9u8c61uff0cu56e0u4e3au817eu8bafu5730u56feSDKu9700u8981u5b83
// u8fd9u662fu4e00u4e2au6a21u62dfu5bf9u8c61uff0cu5c06u4f1au5931u8d25uff0cu4f46u5141u8bb8u6211u4eecu7ee7u7eedu6d4bu8bd5
global.wx = {
  request: function(options) {
    // u6a21u62dfu5931u8d25u56deu8c03
    if (options.fail) {
      setTimeout(() => {
        options.fail({ errMsg: 'request:fail u6a21u62dfu5931u8d25' });
      }, 100);
    }
  }
};

// u6267u884cu6d4bu8bd5
runTests().catch(error => {
  console.error('u6d4bu8bd5u8fc7u7a0bu4e2du53d1u751fu9519u8bef:', error);
});
