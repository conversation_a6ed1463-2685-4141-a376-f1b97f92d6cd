/**
 * 调试地理位置服务
 * 详细输出日志信息
 */

const geoService = require('../services/geo-service');

async function debugTest() {
  console.log('=== 调试地理位置服务 ===');

  // 测试一个新地址
  const testAddress = '四川省攀枝花市';
  console.log(`\n测试地址: ${testAddress}`);

  try {
    const result = await geoService.getAddressLocation(testAddress);
    console.log('返回结果:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('测试出错:', error.message);
    console.error('错误堆栈:', error.stack);
  }
}

debugTest();
