/**
 * API接口测试
 * 测试用户注册、登录等API接口
 */

const axios = require('axios');

// API基础URL
const API_BASE_URL = 'http://localhost:3000/api/v1';

// 测试数据
const testUser = {
  registerType: 'email',
  email: '<EMAIL>',
  password: '123456',
  name: 'API测试用户',
  gender: '男',
  birthDate: '1990-10-01',
  birthTime: '12:30',
  birthPlace: '四川省成都市',
  additionalInfo: {
    occupation: '软件工程师',
    hobbies: '编程、阅读'
  }
};

/**
 * 测试用户注册API
 */
async function testUserRegister() {
  console.log('🧪 测试用户注册API...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/user/register`, testUser);
    
    console.log('✅ 注册成功');
    console.log('📝 响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data.data; // 返回用户数据
    
  } catch (error) {
    if (error.response) {
      console.log('❌ 注册失败:', error.response.data.message);
      console.log('📝 响应数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 请求失败:', error.message);
    }
    return null;
  }
}

/**
 * 测试用户登录API
 */
async function testUserLogin() {
  console.log('\n🔐 测试用户登录API...');
  
  try {
    const loginData = {
      loginType: 'email',
      identifier: testUser.email,
      password: testUser.password
    };
    
    const response = await axios.post(`${API_BASE_URL}/user/login`, loginData);
    
    console.log('✅ 登录成功');
    console.log('📝 响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data.data; // 返回用户数据
    
  } catch (error) {
    if (error.response) {
      console.log('❌ 登录失败:', error.response.data.message);
      console.log('📝 响应数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 请求失败:', error.message);
    }
    return null;
  }
}

/**
 * 测试获取用户信息API
 */
async function testGetUser(userId) {
  console.log('\n📋 测试获取用户信息API...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/user/${userId}`);
    
    console.log('✅ 获取成功');
    console.log('📝 响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data.data;
    
  } catch (error) {
    if (error.response) {
      console.log('❌ 获取失败:', error.response.data.message);
      console.log('📝 响应数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 请求失败:', error.message);
    }
    return null;
  }
}

/**
 * 测试地理编码API
 */
async function testGeocode() {
  console.log('\n🌍 测试地理编码API...');
  
  try {
    const geocodeData = {
      address: '四川省成都市'
    };
    
    const response = await axios.post(`${API_BASE_URL}/geocode`, geocodeData);
    
    console.log('✅ 地理编码成功');
    console.log('📝 响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data.data;
    
  } catch (error) {
    if (error.response) {
      console.log('❌ 地理编码失败:', error.response.data.message);
      console.log('📝 响应数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.log('❌ 请求失败:', error.message);
    }
    return null;
  }
}

/**
 * 测试错误情况
 */
async function testErrorCases() {
  console.log('\n🚫 测试错误情况...');
  
  // 测试重复注册
  console.log('\n1. 测试重复注册:');
  await testUserRegister();
  
  // 测试错误密码登录
  console.log('\n2. 测试错误密码登录:');
  try {
    const wrongLoginData = {
      loginType: 'email',
      identifier: testUser.email,
      password: 'wrongpassword'
    };
    
    const response = await axios.post(`${API_BASE_URL}/user/login`, wrongLoginData);
    console.log('❌ 意外登录成功');
  } catch (error) {
    if (error.response) {
      console.log('✅ 正确拒绝错误密码:', error.response.data.message);
    }
  }
  
  // 测试获取不存在的用户
  console.log('\n3. 测试获取不存在的用户:');
  try {
    const response = await axios.get(`${API_BASE_URL}/user/nonexistent_user`);
    console.log('❌ 意外获取到用户');
  } catch (error) {
    if (error.response) {
      console.log('✅ 正确返回用户不存在:', error.response.data.message);
    }
  }
}

/**
 * 运行所有API测试
 */
async function runAllApiTests() {
  console.log('🚀 API接口测试开始');
  console.log('=' .repeat(50));
  
  try {
    // 1. 测试地理编码API
    await testGeocode();
    
    // 2. 测试用户注册
    const registeredUser = await testUserRegister();
    
    if (registeredUser) {
      // 3. 测试用户登录
      await testUserLogin();
      
      // 4. 测试获取用户信息
      await testGetUser(registeredUser.userId);
    }
    
    // 5. 测试错误情况
    await testErrorCases();
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 API测试完成！');
    
  } catch (error) {
    console.error('\n❌ 测试过程中发生严重错误:', error.message);
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    const response = await axios.get('http://localhost:3000');
    console.log('✅ 服务器运行正常');
    return true;
  } catch (error) {
    console.log('❌ 服务器未运行，请先启动服务器: npm start');
    return false;
  }
}

// 主函数
async function main() {
  console.log('🔍 检查服务器状态...');
  
  const serverRunning = await checkServer();
  if (!serverRunning) {
    process.exit(1);
  }
  
  await runAllApiTests();
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllApiTests,
  testUserRegister,
  testUserLogin,
  testGetUser,
  testGeocode
};
