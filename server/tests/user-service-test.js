/**
 * 用户服务测试
 * 测试用户注册、登录等功能
 */

const userService = require('../services/user-service');
const logger = require('../utils/logger');

// 测试用例
const testUsers = [
  {
    name: '邮箱注册测试',
    data: {
      registerType: 'email',
      email: '<EMAIL>',
      password: '123456',
      name: '张三',
      gender: '男',
      birthDate: '1990-10-01',
      birthTime: '12:30',
      birthPlace: '四川省成都市',
      additionalInfo: {
        hobby: '读书'
      }
    }
  },
  {
    name: '手机号注册测试',
    data: {
      registerType: 'phone',
      phone: '13800138000',
      password: '123456',
      name: '李四',
      gender: '女',
      birthDate: '1992-05-15',
      birthTime: '08:00',
      birthPlace: '北京市海淀区'
    }
  },
  {
    name: '微信注册测试',
    data: {
      registerType: 'wechat',
      wechatOpenId: 'wx_test_openid_123',
      wechatUnionId: 'wx_test_unionid_456',
      name: '王五',
      gender: '男',
      birthDate: '1988-12-25',
      birthPlace: '上海市浦东新区'
    }
  }
];

/**
 * 测试用户注册
 */
async function testUserRegistration() {
  console.log('🧪 ===== 用户注册测试开始 =====\n');
  
  const registeredUsers = [];
  
  for (let i = 0; i < testUsers.length; i++) {
    const testCase = testUsers[i];
    console.log(`${i + 1}. 测试: ${testCase.name}`);
    
    try {
      const result = await userService.createUser(testCase.data);
      
      if (result.success) {
        console.log(`   ✅ 注册成功`);
        console.log(`   📝 用户ID: ${result.user.userId}`);
        console.log(`   📍 出生地坐标: (${result.user.birthCoordinates.latitude}, ${result.user.birthCoordinates.longitude})`);
        console.log(`   📅 创建时间: ${result.user.createdAt}`);
        
        registeredUsers.push(result.user);
      } else {
        console.log(`   ❌ 注册失败: ${result.error}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 注册异常: ${error.message}`);
    }
    
    console.log('');
  }
  
  console.log(`📊 注册测试完成，成功注册 ${registeredUsers.length}/${testUsers.length} 个用户\n`);
  return registeredUsers;
}

/**
 * 测试用户登录
 */
async function testUserLogin(registeredUsers) {
  console.log('🔐 ===== 用户登录测试开始 =====\n');
  
  const loginTests = [
    {
      name: '邮箱登录测试',
      data: {
        loginType: 'email',
        identifier: '<EMAIL>',
        password: '123456'
      }
    },
    {
      name: '手机号登录测试',
      data: {
        loginType: 'phone',
        identifier: '13800138000',
        password: '123456'
      }
    },
    {
      name: '错误密码测试',
      data: {
        loginType: 'email',
        identifier: '<EMAIL>',
        password: 'wrongpassword'
      }
    },
    {
      name: '不存在用户测试',
      data: {
        loginType: 'email',
        identifier: '<EMAIL>',
        password: '123456'
      }
    }
  ];
  
  for (let i = 0; i < loginTests.length; i++) {
    const testCase = loginTests[i];
    console.log(`${i + 1}. 测试: ${testCase.name}`);
    
    try {
      const result = await userService.loginUser(testCase.data);
      
      if (result.success) {
        console.log(`   ✅ 登录成功`);
        console.log(`   👤 用户: ${result.user.name} (${result.user.userId})`);
      } else {
        console.log(`   ❌ 登录失败: ${result.error}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 登录异常: ${error.message}`);
    }
    
    console.log('');
  }
  
  console.log('🔐 登录测试完成\n');
}

/**
 * 测试获取用户信息
 */
async function testGetUser(registeredUsers) {
  console.log('📋 ===== 获取用户信息测试开始 =====\n');
  
  for (let i = 0; i < registeredUsers.length; i++) {
    const user = registeredUsers[i];
    console.log(`${i + 1}. 获取用户信息: ${user.name} (${user.userId})`);
    
    try {
      const result = await userService.getUserById(user.userId);
      
      if (result) {
        console.log(`   ✅ 获取成功`);
        console.log(`   👤 姓名: ${result.name}`);
        console.log(`   📧 联系方式: ${result.email || result.phone || '微信用户'}`);
        console.log(`   📍 出生地: ${result.birthPlace}`);
        console.log(`   🌍 坐标: (${result.birthCoordinates.latitude}, ${result.birthCoordinates.longitude})`);
      } else {
        console.log(`   ❌ 用户不存在`);
      }
      
    } catch (error) {
      console.log(`   ❌ 获取异常: ${error.message}`);
    }
    
    console.log('');
  }
  
  // 测试不存在的用户
  console.log(`${registeredUsers.length + 1}. 获取不存在的用户: nonexistent_user`);
  try {
    const result = await userService.getUserById('nonexistent_user');
    if (result) {
      console.log(`   ❌ 意外获取到用户信息`);
    } else {
      console.log(`   ✅ 正确返回null`);
    }
  } catch (error) {
    console.log(`   ❌ 获取异常: ${error.message}`);
  }
  
  console.log('\n📋 获取用户信息测试完成\n');
}

/**
 * 测试重复注册
 */
async function testDuplicateRegistration() {
  console.log('🚫 ===== 重复注册测试开始 =====\n');
  
  const duplicateTest = {
    registerType: 'email',
    email: '<EMAIL>', // 使用已存在的邮箱
    password: '123456',
    name: '重复用户',
    birthDate: '1995-01-01',
    birthPlace: '广州市天河区'
  };
  
  console.log('1. 尝试使用已存在的邮箱注册');
  
  try {
    const result = await userService.createUser(duplicateTest);
    console.log(`   ❌ 意外注册成功: ${result.user.userId}`);
  } catch (error) {
    console.log(`   ✅ 正确拒绝重复注册: ${error.message}`);
  }
  
  console.log('\n🚫 重复注册测试完成\n');
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 用户服务综合测试开始\n');
  console.log('=' .repeat(50));
  
  try {
    // 1. 测试用户注册
    const registeredUsers = await testUserRegistration();
    
    // 2. 测试用户登录
    await testUserLogin(registeredUsers);
    
    // 3. 测试获取用户信息
    await testGetUser(registeredUsers);
    
    // 4. 测试重复注册
    await testDuplicateRegistration();
    
    console.log('=' .repeat(50));
    console.log('🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生严重错误:', error);
  }
}

// 如果直接运行此文件，则执行所有测试
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  testUserRegistration,
  testUserLogin,
  testGetUser
};
