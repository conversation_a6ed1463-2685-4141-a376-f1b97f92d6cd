/**
 * 测试文件 - 地理位置服务测试
 * 测试地址查询经纬度功能
 */

const geoService = require('../services/geo-service');
const logger = require('../utils/logger');

// 测试地址数组
const testAddresses = [
  '四川省攀枝花市', // 完整地址
  '北京市通州区',      // 另一个完整地址
  '上海市虹口区',       // 省略省份
  '广州天河',          // 省略省份和市后缀
  '深圳市南山区',                  // 空字符串，测试边界情况
  '   ',               // 只有空格，测试边界情况
  '¥%&*（*（&地址XYZABC' // 可能不存在的地址，测试fallback
];

/**
 * 测试地址转经纬度功能
 * @param {String} address 需要测试的地址
 * @returns {Promise<void>}
 */
async function testAddressGeocoding(address) {
  try {
    console.log(`\n正在测试地址: "${address}"`); 
    const startTime = Date.now();
    
    const result = await geoService.getAddressLocation(address);
    const endTime = Date.now();
    
    if (!result) {
      console.log(`❌ 查询失败: 返回null或undefined`);
      return;
    }
    
    const { latitude, longitude } = result;
    console.log(`✅ 查询成功: 纬度=${latitude}, 经度=${longitude}`);
    console.log(`⏱️ 耗时: ${endTime - startTime}ms`);
    
    // 验证结果是否合理
    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      console.log(`⚠️ 警告: 返回的经纬度不是数字类型`);
    }
    
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      console.log(`⚠️ 警告: 经纬度超出有效范围 (纬度:-90~90, 经度:-180~180)`);
    }
    
  } catch (error) {
    console.error(`❌ 测试出错: ${error.message}`);
  }
}

/**
 * 运行所有测试
 */
async function runTests() {
  console.log('==== 地理位置服务测试开始 ====');
  
  // 顺序测试所有地址
  for (const address of testAddresses) {
    await testAddressGeocoding(address);
  }
  
  console.log('\n==== 地理位置服务测试完成 ====');
}

// 执行测试
runTests().catch(error => {
  console.error('测试过程中发生错误:', error);
});
