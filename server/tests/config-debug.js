/**
 * 配置调试脚本
 * 检查环境变量和配置对象的状态
 */

require('dotenv').config();
const config = require('../config');

console.log('=== 配置调试信息 ===');
console.log('1. 环境变量:');
console.log('   process.env.TENCENT_MAP_KEY:', process.env.TENCENT_MAP_KEY ? 'SET' : 'NOT SET');
console.log('   实际值长度:', process.env.TENCENT_MAP_KEY ? process.env.TENCENT_MAP_KEY.length : 0);

console.log('\n2. 配置对象:');
console.log('   config:', JSON.stringify(config, null, 2));

console.log('\n3. 腾讯地图配置:');
console.log('   config.tencent:', config.tencent);
console.log('   config.tencent?.mapKey:', config.tencent?.mapKey ? 'SET' : 'NOT SET');

console.log('\n4. 在geo-service中的获取逻辑:');
const tencentMapKey = config.tencentMapKey || process.env.TENCENT_MAP_KEY;
console.log('   最终获取的key:', tencentMapKey ? 'SET' : 'NOT SET');
console.log('   key长度:', tencentMapKey ? tencentMapKey.length : 0);

console.log('\n5. 测试geo-service导入:');
try {
  const geoService = require('../services/geo-service');
  console.log('   geo-service导入成功');
} catch (error) {
  console.log('   geo-service导入失败:', error.message);
}
